// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import ColorMixer from "../ColorMixer";
import GameManager from "../GameManager";
import GameUtils from "../Game/GameUtils";
import CustomSpriteData from "../CustomSpriteData";

const {ccclass, property} = cc._decorator;

@ccclass
export default class Stuff extends cc.Component {

    private static _curStuffId = -1;
    
    @property(cc.String)
    tag: string = '';

    @property(cc.Integer)
    model_id: number = 0;

    selfScript: Stuff = null;

    isRunning: boolean = true;

    get stuff_id(): number {
        if(this._stuff_id < 0) {
            Stuff._curStuffId += 1;
            this._stuff_id = Stuff._curStuffId;
        }
        return this._stuff_id;
    };
    private _stuff_id: number = -1;

    gameUpdateCallbackId = -1;

    get centerPosition(): cc.Vec2 {
        return this.GetCenterPosition();
    }

    isNodeResortNecessary = true;

    get airPosition(): cc.Vec2 {
        return this._rootPosition.add(cc.v2(0, this.height));
    }

    get rootPosition(): cc.Vec2 {
        return this._rootPosition;
    }

    set rootPosition(value: cc.Vec2) {
        // console.log(`set pos: ${value}`);
        // this._rootPosition = value;

        let finalPos = this.SetPos(value);
        this._rootPosition = finalPos;
    }

    height: number = 0;
    
    hitboxRadius: number = 0;

    nodeSortIndex = -1;

    // protected _rootPosition = cc.v2();
    protected get _rootPosition(): cc.Vec2 {
        return this.__rootPosition;
    }

    protected set _rootPosition(value: cc.Vec2) {
        this.__rootPosition = value;
        this.isNodeResortNecessary = true;
    }
    
    private __rootPosition = cc.v2();

    constructor() {
        super();
        this.selfScript = this;
    }

    /** 在 OnLoad 时自动执行 */
    protected InitOnLoad() {
        // console.log(`Stuff InitOnLoad ${this.node.name}`);
        this.LoadColorMixerComp(this.node);
        GameManager.instance.LateFrameCall(()=>{
            this.LoadColorMixerComp(this.node);
        });
    }

    protected onLoad(): void {
        this.InitOnLoad();
    }

    protected start(): void {

    }

    /** 需要手动执行的初始化代码 */
    Init(..._args: any[]) {
        
    }

    protected gameUpdate(dt: number) {

    }

    LoadColorMixerComp(node: cc.Node) {
        let spine = node.getComponent(sp.Skeleton);
        let sprite = node.getComponent(cc.Sprite);
        // if(spine) {
        //     let colorMixerComp = node.getComponent(CustomSpriteData);
        //     if(!colorMixerComp) {
        //         colorMixerComp = node.addComponent(CustomSpriteData);
        //         colorMixerComp.Init(GameUtils.rootGameWorld.customSpriteMaterial, cc.Color.WHITE, 0);
        //         colorMixerComp.LoadNode();
        //         colorMixerComp.ratio = 0.4;
        //         colorMixerComp.color = cc.Color.BLUE;
        //     }
        // } else 
        if(sprite) {
            let colorMixerComp = node.getComponent(CustomSpriteData);
            if(!colorMixerComp) {
                colorMixerComp = node.addComponent(CustomSpriteData);
                colorMixerComp.Init(GameUtils.rootGameWorld.customSpriteMaterial, cc.Color.WHITE, 0);
                colorMixerComp.LoadNode();
                colorMixerComp.ratio = 0.4;
                colorMixerComp.color = cc.Color.RED;
                // colorMixerComp.isSetHSV = true;
                // colorMixerComp.hsv = {h: 0, s: 500, v: 400};
            }
        }
        node.children.forEach((e)=>{
            this.LoadColorMixerComp(e);
        });
    }

    protected SetPos(pos: cc.Vec2): cc.Vec2 {
        let node = this.node;
        node.x = pos.x; 
        node.y = pos.y;
        // GameUtils.rootGameWorld.TryReSortSelf(this);
        // GameUtils.rootGameWorld.ReSortStuffs();
        return cc.v2(node.x, node.y);
    }

    TP(pos: cc.Vec2) {
        let node = this.node;
        node.x = pos.x; 
        node.y = pos.y;
        this._rootPosition = pos.clone();
    }

    SetRootPosition(pos: cc.Vec2) {
        this._rootPosition = pos.clone();
    }

    GetCenterPosition() {
        return this.rootPosition.add(cc.v2(0, 55 + this.height));
    }

    
    SetHeight(height: number) {
        this.height = height;
    }

    DestroySelf() {
        // this.node.active = false;
        GameManager.instance.LateFrameCall(()=>{
            this.node.destroy();
        });
        GameManager.instance.RemoveUpdateCallbackByID(this.gameUpdateCallbackId);
        this.isRunning = false;
    }

    protected onDestroy(): void {
        if(this.isRunning) {
            GameManager.instance.RemoveUpdateCallbackByID(this.gameUpdateCallbackId);
            this.isRunning = false;
        }
    }
}
