{"version": "1.1.1", "windows": {"main": {"main": true, "url": "app://editor/index.html", "windowType": "dockable", "x": 0, "y": 0, "width": 1920, "height": 1040, "layout": {"children": [{"children": [{"children": [{"active": 0, "children": ["assets"], "height": 652, "type": "panel", "width": 242}, {"active": 0, "children": ["hierarchy"], "height": 652, "type": "panel", "width": 310}], "height": 652, "type": "dock-h", "width": 555}, {"active": 0, "children": ["console", "timeline", "game-window"], "height": 267, "type": "panel", "width": 555}], "height": 922, "type": "dock-v", "width": 555}, {"active": 0, "children": ["scene", "package-asset.export"], "height": 922, "type": "panel", "width": 939}, {"active": 0, "children": ["inspector", "node-library", "cocos-services"], "height": 922, "type": "panel", "width": 404}], "type": "dock-h"}, "panels": ["assets", "hierarchy", "console", "timeline", "game-window", "scene", "package-asset.export", "inspector", "node-library", "cocos-services"]}}, "panels": {"builder": {"x": 515, "y": 224, "width": 514, "height": 737}, "project-settings": {"x": 347, "y": 167, "width": 1074, "height": 772}, "coolplayable-tool": {"x": 752, "y": 290, "width": 416, "height": 439}, "preferences": {"x": 1051, "y": 240, "width": 600, "height": 565}, "sprite-editor": {"x": 960, "y": 159, "width": 844, "height": 747}, "package-asset.import": {"x": 590, "y": 256, "width": 520, "height": 575}, "package-asset.export": {"x": 752, "y": 250, "width": 416, "height": 519}, "store": {"x": 452, "y": 140, "width": 1016, "height": 739}, "timeline": {"x": 1065, "y": 313, "width": 520, "height": 339}, "im-plugin": {"x": 452, "y": 140, "width": 1016, "height": 739}}, "panelLabelWidth": {"inspector": "40.0697%", "builder": null, "project-settings": null, "preferences": null}, "panelLabelWidth.inspector": "40.0697%", "panelLabelWidth.builder": null, "panelLabelWidth.project-settings": null, "panelLabelWidth.preferences": null}