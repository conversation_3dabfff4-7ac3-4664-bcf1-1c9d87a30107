

// export declare class MyAssembler extends cc.Assembler2D {
//     updateRenderData (sprite): void;
//     updateUVs (sprite): void;
//     updateVerts (sprite): void;
//     fillBuffers (sprite, renderer): void;
// }

// @ts-ignore
let gfx = cc.gfx;

let vfmtPosUvColorTex = new gfx.VertexFormat([
    { name: gfx.ATTR_POSITION, type: gfx.ATTR_TYPE_FLOAT32, num: 2 },
    { name: gfx.ATTR_UV0, type: gfx.ATTR_TYPE_FLOAT32, num: 2 },
    { name: gfx.ATTR_COLOR, type: gfx.ATTR_TYPE_UINT8, num: 4, normalize: true },
    { name: gfx.ATTR_COLOR0, type: gfx.ATTR_TYPE_UINT8, num: 4, normalize: true },
    { name: gfx.ATTR_COLOR1, type: gfx.ATTR_TYPE_UINT8, num: 4, normalize: true },
    { name: gfx.ATTR_TEX_COORD1, type: gfx.ATTR_TYPE_FLOAT32, num: 4},
    // { name: gfx.ATTR_TEX_COORD2, type: gfx.ATTR_TYPE_FLOAT32, num: 4},
]);

export function MyAssembler(): typeof cc.Assembler2D {
    const Assembler2D = cc.Assembler2D;
    return class extends Assembler2D {

        // 2 + 1 + 1 + 4 + 4
        floatsPerVert = 11; // 一个顶点数据占用的浮点数空间（每 32 位算一个 float）
        verticesCount = 4; // 固定 4
        indicesCount = 6; // 固定 6

        uvOffset = 2; // uv 偏移
        colorOffset = 4; // 色彩偏移（节点颜色）
        color0Offset = 5; // 色彩偏移（混合颜色）
        color1Offset = 6; // 色彩偏移（记录布尔值）

        texCoordOffset = 7; // 自定义数据偏移

        mixColor: cc.Color = cc.Color.WHITE;
        ratio: number = 0;
        hsv_h: number = 0;
        hsv_s: number = 0;
        hsv_v: number = 0;
        // ambientColor: cc.Color = cc.Color.WHITE; // 环境光，如果有其它光源可以叠加在此颜色上
        
        isLightReceiver = false; // 是否是光接收器
        isChangeOpacity: boolean = false; // 光接收器是否影响不透明度
        isToFrag: boolean = false; // 顶点计算还是片元计算

        initData() {
            let data = this._renderData;
            // createFlexData支持创建指定格式的renderData
            data.createFlexData(0, this.verticesCount, this.indicesCount, this.getVfmt());

            // data.createQuadData(0, this.verticesFloats, this.indicesCount);

            // createFlexData不会填充顶点索引信息，手动补充一下
            let indices = data.iDatas[0];
            data.initQuadIndices(indices);
            // let count = indices.length / 6;
            // for (let i = 0, idx = 0; i < count; i++) {
            //     let vertextID = i * 4;
            //     indices[idx++] = vertextID;
            //     indices[idx++] = vertextID+1;
            //     indices[idx++] = vertextID+2;
            //     indices[idx++] = vertextID+1;
            //     indices[idx++] = vertextID+3;
            //     indices[idx++] = vertextID+2;
            // }
        }

        getVfmt() {
            return vfmtPosUvColorTex;
        }

        updateRenderData (sprite) {
            this.packToDynamicAtlas(sprite, sprite._spriteFrame);

            if (sprite._vertsDirty) {
                this.updateUVs(sprite);
                this.updateMixColor(sprite);
                this.updateLightReceiverAttr(sprite);
                this.updateTexCoords(sprite);
                this.updateVerts(sprite);
                this.updateWorldVerts(sprite);
                sprite._vertsDirty = false;
            }
        }

        // 重载getBuffer(), 返回一个能容纳自定义顶点数据的buffer
        // 默认fillBuffers()方法中会调用到
        getBuffer() {
            return cc.renderer._handle.getBuffer("mesh", this.getVfmt());
        }

        simpleUpdateUVs (sprite) {
            let uv = sprite._spriteFrame.uv;
            let uvOffset = this.uvOffset;
            let floatsPerVert = this.floatsPerVert;
            let verts = this._renderData.vDatas[0];
            for (let i = 0; i < 4; i++) {
                let srcOffset = i * 2;
                let dstOffset = floatsPerVert * i + uvOffset;
                verts[dstOffset] = uv[srcOffset];
                verts[dstOffset + 1] = uv[srcOffset + 1];
            }
        }

        // 在此处将参数传递给顶点数据
        updateTexCoords(sprite) {
            // vec4 传递 hsv 和 ratio
            let ratio = this.ratio;
            let hsv_h = this.hsv_h;
            let hsv_s = this.hsv_s;
            let hsv_v = this.hsv_v;
            let texCoordOffset = this.texCoordOffset;
            let floatsPerVert = this.floatsPerVert;
            let verts = this._renderData.vDatas[0];
            let dstOffset;
            for (let i = 0; i < 4; i++) {
                dstOffset = floatsPerVert * i + texCoordOffset;
                verts[dstOffset] = hsv_h;
                verts[dstOffset + 1] = hsv_s;
                verts[dstOffset + 2] = hsv_v;
                verts[dstOffset + 3] = ratio;
            }
        }

        // 传递颜色
        updateMixColor(sprite) {
            let color0 = this.mixColor._val;
            // let color1 = this.ambientColor._val;
            let verts = this._renderData.uintVDatas[0];
            if (!verts) return;
            let floatsPerVert = this.floatsPerVert;
            let color0Offset = this.color0Offset;
            // let color1Offset = this.color1Offset;
            for (let i = color0Offset, l = verts.length; i < l; i += floatsPerVert) {
                verts[i] = color0;
            }
            // for (let i = color1Offset, l = verts.length; i < l; i += floatsPerVert) {
            //     verts[i] = color1;
            // }
        }

        // 传递光接收器属性
        updateLightReceiverAttr(sprite) {
            let isLightReceiver = this.isLightReceiver;
            let isChangeOpacity = this.isChangeOpacity;
            let isToFrag = this.isToFrag;
            let data0 = 0;
            // 按位存储布尔值
            let boolValues: boolean[] = [isLightReceiver, isChangeOpacity, isToFrag];
            for (let i = 0; i < 8; i++) {
                if(boolValues[i]) data0 |= 128 >> i;
            }
            let data = data0 << 24;
            let color1Offset = this.color1Offset;
            let floatsPerVert = this.floatsPerVert;
            let verts = this._renderData.vDatas[0];
            let dstOffset;
            for (let i = 0; i < 4; i++) {
                dstOffset = floatsPerVert * i + color1Offset;
                verts[dstOffset] = data;
            }
        }

        updateUVs(sprite) {
            // uv0调用基类方法写入
            this.simpleUpdateUVs(sprite);
            // 填入自己的uv1数据
            // ...
            // 方法类似uv0写入，详见Demo
            // https://github.com/caogtaa/CCBatchingTricks
        }

        updateVerts (sprite) {
            let node = sprite.node,
                cw = node.width, ch = node.height,
                appx = node.anchorX * cw, appy = node.anchorY * ch,
                l, b, r, t;
            if (sprite.trim) {
                l = -appx;
                b = -appy;
                r = cw - appx;
                t = ch - appy;
            }
            else {
                let frame = sprite.spriteFrame,
                    ow = frame._originalSize.width, oh = frame._originalSize.height,
                    rw = frame._rect.width, rh = frame._rect.height,
                    offset = frame._offset,
                    scaleX = cw / ow, scaleY = ch / oh;
                let trimLeft = offset.x + (ow - rw) / 2;
                let trimRight = offset.x - (ow - rw) / 2;
                let trimBottom = offset.y + (oh - rh) / 2;
                let trimTop = offset.y - (oh - rh) / 2;
                l = trimLeft * scaleX - appx;
                b = trimBottom * scaleY - appy;
                r = cw + trimRight * scaleX - appx;
                t = ch + trimTop * scaleY - appy;
            }

            let local = this._local;
            local[0] = l;
            local[1] = b;
            local[2] = r;
            local[3] = t;
        }
    
        updateColor (comp: cc.RenderComponent, color: any) {
            let uintVerts = this._renderData.uintVDatas[0];
            if (!uintVerts) return;
            color = color ||comp.node.color._val;
            let floatsPerVert = this.floatsPerVert;
            let colorOffset = this.colorOffset;
            // for (let i = colorOffset, l = floatsPerVert * 4; i < l; i += floatsPerVert) {
            //     uintVerts[i] = color;
            // }
            for (let i = colorOffset, l = uintVerts.length; i < l; i += floatsPerVert) {
                uintVerts[i] = color;
            }
            // console.log(`uintVerts.length: ${uintVerts.length}`);
            // let texCoordOffset = this.texCoordOffset;
            // for (let i = floatsPerVert * 4 + texCoordOffset, l = floatsPerVert * 8; i < l; i += floatsPerVert) {
            //     uintVerts[i] = color;
            // }
        }


        updateWorldVerts(comp) {
            if (CC_NATIVERENDERER) {
                this.updateWorldVertsNative(comp);
            } else {
                this.updateWorldVertsWebGL(comp);
            }
        }

        updateWorldVertsWebGL (comp: cc.RenderComponent) {
            let local = this._local;
            let verts = this._renderData.vDatas[0];

            let matrix = comp.node._worldMatrix;
            let matrixm = matrix.m,
                a = matrixm[0], b = matrixm[1], c = matrixm[4], d = matrixm[5],
                tx = matrixm[12], ty = matrixm[13];

            let vl = local[0], vr = local[2],
                vb = local[1], vt = local[3];
            
            let justTranslate = a === 1 && b === 0 && c === 0 && d === 1;

            // 新增 index 偏移
            let floatsPerVert = this.floatsPerVert;
            let index = 0;
            if (justTranslate) {
                // left bottom
                verts[index] = vl + tx;
                verts[index + 1] = vb + ty;
                index += floatsPerVert;
                // right bottom
                verts[index] = vr + tx;
                verts[index + 1] = vb + ty;
                index += floatsPerVert;
                // left top
                verts[index] = vl + tx;
                verts[index + 1] = vt + ty;
                index += floatsPerVert;
                // right top
                verts[index] = vr + tx;
                verts[index + 1] = vt + ty;
            } else {
                let al = a * vl, ar = a * vr,
                bl = b * vl, br = b * vr,
                cb = c * vb, ct = c * vt,
                db = d * vb, dt = d * vt;

                // left bottom
                verts[index] = al + cb + tx;
                verts[index + 1] = bl + db + ty;
                index += floatsPerVert;
                // right bottom
                verts[index] = ar + cb + tx;
                verts[index + 1] = br + db + ty;
                index += floatsPerVert;
                // left top
                verts[index] = al + ct + tx;
                verts[index + 1] = bl + dt + ty;
                index += floatsPerVert;
                // right top
                verts[index] = ar + ct + tx;
                verts[index + 1] = br + dt + ty;
            }
        }
        // native场景下使用的updateWorldVerts
        // copy from \jsb-adapter-master\engine\assemblers\assembler-2d.js
        updateWorldVertsNative(comp) {
            let local = this._local;
            let verts = this._renderData.vDatas[0];
            let floatsPerVert = this.floatsPerVert;
        
            let vl = local[0],
                vr = local[2],
                vb = local[1],
                vt = local[3];
        
            let index: number = 0;
            // left bottom
            verts[index] = vl;
            verts[index+1] = vb;
            index += floatsPerVert;
            // right bottom
            verts[index] = vr;
            verts[index+1] = vb;
            index += floatsPerVert;
            // left top
            verts[index] = vl;
            verts[index+1] = vt;
            index += floatsPerVert;
            // right top
            verts[index] = vr;
            verts[index+1] = vt;
        }

    }

}

// export default class MyAssembler;

// export class MyAssembler extends cc.Assembler2D {
//     updateRenderData (sprite) {
//         this.packToDynamicAtlas(sprite, sprite._spriteFrame);

//         if (sprite._vertsDirty) {
//             this.updateUVs(sprite);
//             this.updateVerts(sprite);
//             sprite._vertsDirty = false;
//         }
//     }

//     updateUVs (sprite) {
//         let uv = sprite._spriteFrame.uv;
//         let uvOffset = this.uvOffset;
//         let floatsPerVert = this.floatsPerVert;
//         let verts = this._renderData.vDatas[0];
//         for (let i = 0; i < 4; i++) {
//             let srcOffset = i * 2;
//             let dstOffset = floatsPerVert * i + uvOffset;
//             verts[dstOffset] = uv[srcOffset];
//             verts[dstOffset + 1] = uv[srcOffset + 1];
//         }
//     }

//     updateVerts (sprite) {
//         let node = sprite.node,
//             cw = node.width, ch = node.height,
//             appx = node.anchorX * cw, appy = node.anchorY * ch,
//             l, b, r, t;
//         if (sprite.trim) {
//             l = -appx;
//             b = -appy;
//             r = cw - appx;
//             t = ch - appy;
//         }
//         else {
//             let frame = sprite.spriteFrame,
//                 ow = frame._originalSize.width, oh = frame._originalSize.height,
//                 rw = frame._rect.width, rh = frame._rect.height,
//                 offset = frame._offset,
//                 scaleX = cw / ow, scaleY = ch / oh;
//             let trimLeft = offset.x + (ow - rw) / 2;
//             let trimRight = offset.x - (ow - rw) / 2;
//             let trimBottom = offset.y + (oh - rh) / 2;
//             let trimTop = offset.y - (oh - rh) / 2;
//             l = trimLeft * scaleX - appx;
//             b = trimBottom * scaleY - appy;
//             r = cw + trimRight * scaleX - appx;
//             t = ch + trimTop * scaleY - appy;
//         }

//         let local = this._local;
//         local[0] = l;
//         local[1] = b;
//         local[2] = r;
//         local[3] = t;
//         this.updateWorldVerts(sprite);
//     }
// }