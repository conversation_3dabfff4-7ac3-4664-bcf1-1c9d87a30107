// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { SimpleLightType } from "./SimpleLightSystemManager";

const {ccclass, property, executeInEditMode, disallowMultiple} = cc._decorator;

@ccclass
export default class SimpleLightSource extends cc.Component {
    // @property({readonly: true, multiline: false})
    // information: string = `光照系统脚本`;

    @property({type: cc.Enum(SimpleLightType)})
    lightType: SimpleLightType = SimpleLightType.none;

    @property(cc.Color)
    lightColor: cc.Color = cc.Color.WHITE;

    @property(cc.Float)
    lightIntensity: number = 0;

    @property
    innerRadius: number = 2;

    @property
    outerRadius: number = 50;

    @property({type: cc.Integer, range: [0, 10, 1]})
    falloffExponent: number = 2;
}