// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import SimpleLightSystemManager, { SimpleLightInfo, SimpleLightType } from "./SimpleLightSystemManager";

const {ccclass, property, executeInEditMode, disallowMultiple} = cc._decorator;

@ccclass
@executeInEditMode
export default class SimpleLightSource extends cc.Component {
    // @property({readonly: true, multiline: false})
    // information: string = `光照系统脚本`;

    @property({type: cc.Enum(SimpleLightType)})
    lightType: SimpleLightType = SimpleLightType.none;

    @property(cc.Color)
    lightColor: cc.Color = cc.Color.WHITE;

    @property({type: cc.Float, range: [0, 10, 0.01]})
    lightIntensity: number = 0;

    @property
    lightYScale: number = 0.8;

    @property
    innerRadius: number = 2;

    @property
    outerRadius: number = 50;

    @property({type: cc.Integer, range: [0, 10, 1]})
    falloffExponent: number = 2;

    info: SimpleLightInfo = null;

    get worldPosition(): cc.Vec3 {
        return this.node.convertToWorldSpaceAR(cc.v3());
    }
    
    protected onLoad(): void {
        this.info = SimpleLightSystemManager.instance.regLight(this);
        if(!this.info) {
            cc.warn('注册失败！');
        }
    }
    
}