import { MyAssembler } from "../Extension/MyAssembler";

export class ClassFactory {
    public static get instance(): ClassFactory {
        if(!this._instance) {
            this._instance = new ClassFactory();
        }
        return this._instance;
    }
    private static _instance: ClassFactory = null;

    _inited = false;

    _Init() {
        console.log('ClassFactory Init!');
        this._inited = true;
    }

    _LogError() {
        console.error('ClassFactory is NOT Inited!');
    }

    get MyAssembler(): typeof MyAssembler {
        if(this._inited) {
            return MyAssembler;
        } else {
            this._LogError();
            return null;
        }
    };

    public static CreateInstance<T>(type: {new(): T}): T {
        return new type();
    }
}

let gameInit = {
    x: ()=> {
        // console.log('init!');
    },
};

cc.game.once(cc.game.EVENT_GAME_INITED, (...args)=>{
    console.log('-------EVENT_GAME_INITED-------');
    // console.log(args);
    if(CC_DEBUG) {
        console.log(cc);
        gameInit.x();

        ClassFactory.instance._Init();
    }
});
