// Copyright (c) 2017-2018 Xiamen Yaji Software Co., Ltd.  

CCEffect %{
  # mix:
  techniques:
  - passes:
    - vert: vs
      frag: fs
      blendState:
        targets:
        - blend: true
      rasterizerState:
        cullMode: none
      properties:
        texture: { value: white }
        dataTexture: { value: white }
        alphaThreshold: { value: 0.5 }
}%


CCProgram vs %{
  precision highp float;

  #include <cc-global>
  #include <cc-local>
  #include <texture>

  // 函数定义必须在 main 函数之前
  vec4 decodeDataTextrue(sampler2D dataTexture);
  vec4 loadTextrue(sampler2D dataTexture, ivec2 uv);
  ivec2 getUVFromOffset(int offset, int lineLength);
  int getOffset(int index);
  int modInt(int a, int b);

  float decodeToFloat(vec4 color);

  in vec3 a_position;
  in vec4 a_color;
  in vec4 a_color0;
  in vec4 a_color1;
  out vec4 v_color;
  out vec4 v_color0;
  out vec4 v_color1;

  #if USE_TINT
  // in vec4 a_color0;
  #endif

  uniform sampler2D dataTexture;

  in vec4 a_texCoord1;
  out vec4 v_texCoord1;

  #if USE_TEXTURE
  in vec2 a_uv0;
  out vec2 v_uv0;
  #endif

  out vec3 v_modelPos; // 传递给片元着色器

  out vec4 v_lightColor; // 顶点光照计算的颜色

  void main () {
    vec4 pos = vec4(a_position, 1);

    #if CC_USE_MODEL
    pos = cc_matViewProj * cc_matWorld * pos;
    #else
    pos = cc_matViewProj * pos;
    #endif

    #if USE_TEXTURE
    v_uv0 = a_uv0;
    #endif

    #if CC_USE_MODEL
    v_modelPos = a_position; // 直接传递
    #endif
    // gl_Position = cc_matViewProj * vec4(a_position, 1.0);

    v_color = a_color;
    v_color0 = a_color0;
    v_color1 = a_color1;
    v_texCoord1 = a_texCoord1;

    // 计算顶点光照颜色
    // vec4 lightColor = vec4(1.0, 1.0, 1.0, 1.0);
    // CCTexture(dataTexture, vec2(0, 0), lightColor);
    // v_lightColor = lightColor;
    v_lightColor = decodeDataTextrue(dataTexture);

    gl_Position = pos;
  }

  // 解码数据纹理
  vec4 decodeDataTextrue(sampler2D dataTexture) {
    
    // vec4 ambientColor = loadTextrue(dataTexture, ivec2(0, 0)); // (0, 0) 处存储的是环境光
    vec4 ambientColor = loadTextrue(dataTexture, getUVFromOffset(getOffset(0), 64)); // (0, 0) 处存储的是环境光

    vec4 lightColor = ambientColor;

    // 布尔解析
    bool pointLigthsIsOn[32];
    ivec2 pointLigthsIsOnUV = getUVFromOffset(getOffset(1), 64);
    vec4 pointLigthsIsOnOrigData = loadTextrue(dataTexture, pointLigthsIsOnUV);
    // vec4 pointLigthsIsOnOrigData = loadTextrue(dataTexture, ivec2(2, 2));
    for(int i = 0; i < 4; i++) {
      if(i == 0) {
        float leftData = pointLigthsIsOnOrigData.r * 255.0;
        bool boolDatas[8];
        float modNum = 128.0;
        for(int j = 0; j < 8; j++) {
          boolDatas[j] = (leftData / modNum) >= 0.01;
          leftData = mod(leftData, modNum);
          modNum /= 2.0;
          pointLigthsIsOn[i *8 + j] = boolDatas[j];
        }
      }
    }

    // vec3 解析
    vec3 pointLightPositions[32];
    vec3 pointLightPosition;
    for(int j = 0; j < 3; j++) {
      ivec2 pointLightPositionUV = getUVFromOffset(getOffset(2) + j, 64);
      vec4 pointLightPositionOrigData = loadTextrue(dataTexture, pointLightPositionUV);
      if(j == 0) {
        pointLightPosition.x = decodeToFloat(pointLightPositionOrigData);
      } else if(j == 1) {
        pointLightPosition.y = decodeToFloat(pointLightPositionOrigData);
      } else if(j == 2) {
        pointLightPosition.z = decodeToFloat(pointLightPositionOrigData);
      }
    }
    
    // 计算点光源
    float dist = length(pointLightPosition - a_position);
    if(dist < 2000000.0) {
      // ivec2 pointLigthsColorUV = getUVFromOffset(getOffset(3), 64);
      // vec4 pointLigthColor = loadTextrue(dataTexture, pointLigthsColorUV);
      // lightColor = pointLigthColor;
      lightColor = vec4(1.0, 1.0, 0.0, 1.0);
    } else {
      // lightColor = vec4(0.0, 0.0, 1.0, 0.5);
    }

    // vec3 pointLightPosition[32];
    // 测试代码
    if(pointLigthsIsOn[0] == true) {
      lightColor = vec4(1.0, 0.0, 0.0, 1.0);
    }
    // lightColor = pointLigthsIsOnOrigData;
    // lightColor = vec4(1.0, 1.0, 1.0, 1.0);
    return lightColor;
  }

  ivec2 getUVFromOffset(int offset, int lineLength) {
    int x = modInt(offset, lineLength);
    int y = offset / lineLength;
    return ivec2(x, y);
  }

  int modInt(int a, int b) {
    return a - (a / b) * b;
  }

  // 获取偏移值的函数，避免数组初始化问题
  int getOffset(int index) {
    if (index == 0) return 0;
    else if (index == 1) return 32;
    else if (index == 2) return 33;
    else if (index == 3) return 129;
    else if (index == 4) return 161;
    else if (index == 5) return 193;
    else if (index == 6) return 225;
    else if (index == 7) return 257;
    else if (index == 8) return 289;
    else return 0; // 默认值
  }

  // 从纹理中指定位置读取颜色值
  vec4 loadTextrue(sampler2D dataTexture, ivec2 uv) {
    // 根据 WebGL 版本选择最佳采样方法
    #if __VERSION__ >= 300
      // WebGL 2.0: 使用 texelFetch 进行精确采样，避免任何插值
      vec4 color = texelFetch(dataTexture, ivec2(uv.x, uv.y), 0);
    #else
      float x = float(uv.x) / 64.0;
      float y = float(uv.y) / 5.0;
      // WebGL 1.0: 使用 texture2D，但确保纹理设置为 NEAREST 过滤
      vec4 color = texture2D(dataTexture, vec2(x, y));
    #endif
    return color;
  }

  float decodeToFloat(vec4 color) {
    float rData = color.r * 256.0 * 256.0 * 256.0 * 256.0;
    float gData = color.g * 256.0 * 256.0 * 256.0;
    float bData = color.b * 256.0 * 256.0;
    float aData = color.a * 256.0;
    return rData + gData + bData + aData;
  }
}%


CCProgram fs %{
  precision highp float;
  
  #include <alpha-test>
  #include <texture>

  struct hsv { float h; float s; float v; };

  vec3 fromHSV (hsv hsvInput);
  hsv toHsv(vec3 color);
  float ArrayMaxLen3(float array[3]);
  float ArrayMinLen3(float array[3]);

  in vec4 v_color;
  in vec4 v_color0;
  in vec4 v_color1;
  // in vec4 v_position;  // 片元坐标
  // in vec4 v_positionWorld;  // 片元的世界坐标
  in vec3 v_modelPos; // 从顶点着色器传来的模型坐标

  in vec4 v_texCoord1;

  // uniform mix {
  //   vec4 mixColor;
  //   float mixRatio;
  // };
  // uniform HSV {
  //   int H;
  //   int S;
  //   int V;
  // };
  // uniform hyalinize {
  //   float hyalinize;
  //   float hyalinizeHeight;
  // };

  in vec4 v_lightColor;

  #if USE_TEXTURE
  in vec2 v_uv0;
  uniform sampler2D texture;
  #endif

  uniform sampler2D dataTexture;

  void main () {
    float mixRatio = v_texCoord1.w;
    vec4 mixColor = v_color0;
    hsv hsvValue = hsv(0.0, 0.0, 0.0);

    float ratio = 0.0;
    // if(mixRatio != 0.0) {
      ratio = mixRatio;
    // }

    vec4 o = vec4(1, 1, 1, 1);

    int H = int(v_texCoord1.x);
    int S = int(v_texCoord1.y);
    int V = int(v_texCoord1.z);

    #if USE_TEXTURE
      CCTexture(texture, v_uv0, o);
    #endif

    o *= v_color; // 颜色信息
    // o.a *= v_color.a;
    ALPHA_TEST(o);
    
    hsvValue = toHsv(o.rgb);
    if(H != 0) {
      hsvValue.h += float(H) / 360.0; // 色相
    }
    if(S != 0) {
      hsvValue.s *= float(100 + S) / 100.0; // 饱和度
    }
    if(V != 0) {
      hsvValue.v *= float(100 + V) / 100.0; // 亮度
    }
    o = vec4(fromHSV(hsvValue), o.a);

    float ax = 1.0;

    #if CC_USE_MODEL
    float y = v_modelPos.y;
    // if(hyalinize >= 1.0) {
    //   ax = 0.0;
    // } else if(hyalinizeHeight > 0.0) {
    //   if(y < hyalinizeHeight) {
    //     ax = 1.0 - hyalinize;
    //   } else {
    //   }
    // }
    #endif

    float a = o.a * ax;

    // 处理光接收器属性
    // v_color1.r 现在直接包含位标志数据 (0-255 范围)
    float data = v_color1.r * 255.0; // 将归一化的颜色值转换回 0-255 范围
    float leftData = data;
    bool isLightReceiver = (leftData / 128.0) >= 1.0;
    leftData = mod(leftData, 128.0);
    bool isChangeOpacity = (leftData / 64.0) >= 1.0;
    leftData = mod(leftData, 64.0);
    bool isToFrag = (leftData / 32.0) >= 1.0;
    // ...后续的解码不需要

    // 得到环境光和环境光强度
    // vec4 ambient = vec4(1.0, 1.0, 1.0, 1.0);
    // CCTexture(dataTexture, vec2(0, 0), ambient);
    // 使用顶点传递的光照颜色
    vec4 ambient = v_lightColor;

    vec3 mixedColorRgb = (o.rgb * (1.0 - ratio) + mixColor.rgb * ratio);
    // vec3 mixedColorRgb = (o.rgb * (1.0 - (ratio > 0.0 ? ratio : 0.0));
    if(isLightReceiver) {
      vec3 outRgb = mixedColorRgb * (ambient.rgb * (isChangeOpacity? 1.0 : ambient.a));
      o = vec4(outRgb, a * (isChangeOpacity? ambient.a : 1.0));
    } else {
      o = vec4(mixedColorRgb, a);
    }
    // o = vec4(mixedColorRgb, a);


    ALPHA_TEST(o);

    gl_FragColor = o;
  }
  
  vec3 fromHSV (hsv hsvInput) {
    float h = hsvInput.h;
    float s = hsvInput.s;
    float v = hsvInput.v;
    float r = 0.0;
    float g = 0.0;
    float b = 0.0;
    float f;
    float p;
    float q;
    float t;
    int i = 0;
    if(s == 0.0) {
        r = g = b = v;
    } else {
      if (v == 0.0) {
        r = g = b = 0.0;
      } else {
        if(h == 1.0) h = 0.0;
        if(h > 1.0) h -= 1.0;
        if(h < 0.0) h += 1.0;
        h *= 6.0;
        s = s;
        v = v;
        i = int(h);

        f = h - float(i);
        p = v * (1.0 - s);  
        q = v * (1.0 - (s * f));  
        t = v * (1.0 - (s * (1.0 - f)));
        if(i == 0) {  
          r = v;  
          g = t;  
          b = p;
        } else if(i == 1) {  
          r = q;  
          g = v;  
          b = p;  
        } else if(i == 2) {  
          r = p;  
          g = v;  
          b = t;
        } else if(i == 3) {
          r = p;  
          g = q;  
          b = v;  
        } else if(i == 4) {
          r = t;  
          g = p;  
          b = v;  
        } else if(i == 5) {
          r = v;  
          g = p;  
          b = q;  
        }
      }
    }
    return vec3(r, g, b);
  }

  hsv toHsv(vec3 color) {
    hsv hsvResult = hsv(0.0, 0.0, 0.0);
    float r = color.r;
    float g = color.g;
    float b = color.b;
    float rgbArray[3];
    rgbArray[0] = r;
    rgbArray[1] = g;
    rgbArray[2] = b;
    float maxV = ArrayMaxLen3(rgbArray);
    float minV = ArrayMinLen3(rgbArray);
    float delta = 0.0;
    hsvResult.v = maxV;
    hsvResult.s = maxV == 0.0 ? 0.0 : ((maxV - minV) / maxV);
    if (hsvResult.s == 0.0) hsvResult.h = 0.0;
    else {
      delta = maxV - minV;
      if (r == maxV) hsvResult.h = (g - b) / delta;
      else if (g == maxV) hsvResult.h = 2.0 + (b - r) / delta;
      else hsvResult.h = 4.0 + (r - g) / delta;
      hsvResult.h /= 6.0;
      if (hsvResult.h < 0.0) hsvResult.h += 1.0;
    }
    return hsvResult;
  }

  float ArrayMaxLen3(float array[3]) {
    return array[0] > array[1] ? (array[0] > array[2] ? array[0] : array[2]) : (array[1] > array[2] ? array[1] : array[2]);
  }

  float ArrayMinLen3(float array[3]) {
    return array[0] < array[1] ? (array[0] < array[2] ? array[0] : array[2]) : (array[1] < array[2] ? array[1] : array[2]);
  }
}%

