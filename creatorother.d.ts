
declare namespace cc {
    export class Assembler {
        getVfmt(): any;
    }

    export class Assembler2D extends Assembler {
        floatsPerVert: number;
        verticesCount: number;
        indicesCount: number;
        uvOffset: number;
        colorOffset: number;
        
        _renderData: cc.RenderData;
        _local: number[];

        constructor();
        get verticesFloats (): number;

        updateRenderData(comp: cc.RenderComponent): void;
        fillBuffers(comp: cc.RenderComponent, renderer: any): void;
        init(comp: cc.RenderComponent): void;
        updateWorldVerts(comp: cc.RenderComponent): void;
        packToDynamicAtlas(comp: cc.RenderComponent, frame: cc.SpriteFrame): void;
        // updateColor(comp: cc.RenderComponent, color: cc.Color): void;
        // 这里你可以加上你需要的其它方法签名
    }

}

declare namespace cc {
    export interface Node {
        _worldMatrix: any;
    }

    export interface RenderComponent extends cc.Component {
        setVertsDirty(): void;
        _resetAssembler(): void;
    }

    export interface Sprite {
        _assembler: cc.Assembler;

        // 此处是新增的自定义字段
        UpdateRenderer(): boolean;
        get mixColor(): cc.Color;
        set mixColor(value: cc.Color);
        get ratio(): number;
        set ratio(value: number);
        get hsv(): {h: number, s: number, v: number};
        set hsv(value: {h: number, s: number, v: number});
        // get ambientColor(): cc.Color;
        // set ambientColor(value: cc.Color);
        isLightReceiver: boolean;
        isChangeOpacity: boolean;
        isToFrag: boolean;
        
        isRendererDirty: boolean;
    }

    export namespace Sprite {
        export enum Type {
            CUSTOM = 0,
        }
    }

    export namespace renderer {
        export const _handle: {
            getBuffer(type: string, format: any): any;
            // _batchedModels?: any[];  // 用于 GameUtils.ts 中的使用
        };
    }

    export interface Color {
        _val: number;
    }
}