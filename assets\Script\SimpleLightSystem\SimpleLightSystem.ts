// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import { TweenObject } from "../LocalUtils";
import SimpleLightSystemManager from "./SimpleLightSystemManager";

const {ccclass, property, executeInEditMode, disallowMultiple} = cc._decorator;

@ccclass
@executeInEditMode
@disallowMultiple
export default class SimpleLightSystem extends cc.Component {
    @property({readonly: true, multiline: false})
    information: string = `光照系统脚本`;

    @property(cc.Material)
    customSpriteMaterial: cc.Material = null;
    

    @property(cc.Color)
    get ambientColor(): cc.Color {
        return this._ambientColor;
    }
    set ambientColor(value: cc.Color) {
        this._ambientColor = value;
        this.SetDirty();
    }
    private _ambientColor: cc.Color = cc.Color.WHITE;

    @property({type: cc.Float, range: [0, 10, 0.01]})
    get ambientIntensity(): number {
        return this._ambientIntensity;
    }
    set ambientIntensity(value: number) {
        this._ambientIntensity = value;
        this.SetDirty();
    }
    private _ambientIntensity: number = 0.4;


    dataTexture: cc.Texture2D = null;

    protected onLoad(): void {
        SimpleLightSystemManager.instance.systemScript = this;
        this.SetDirty();
    }

    protected start() {
        this.SetDirty();
        // cc.tween(new TweenObject(0, (val: number)=>{
        //     this.ambientIntensity = val;
        // })).to(0.8, {value: 1}).to(0.8, {value: 0.2}).union().repeatForever().start();
    }

    protected update(dt: number): void {
        SimpleLightSystemManager.instance.update(dt);
    }

    SetDirty() {
        SimpleLightSystemManager.instance.isDirty = true;
    }
}