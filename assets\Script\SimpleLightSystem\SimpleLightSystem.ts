// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import SimpleLightSystemManager from "./SimpleLightSystemManager";

const {ccclass, property, executeInEditMode, disallowMultiple} = cc._decorator;

@ccclass
@executeInEditMode
@disallowMultiple
export default class SimpleLightSystem extends cc.Component {
    @property({readonly: true, multiline: false})
    information: string = `光照系统脚本`;

    @property(cc.Color)
    ambientColor: cc.Color = cc.Color.WHITE;

    @property({type: cc.Float, range: [0, 10]})
    ambientIntensity: number = 1;

    protected onLoad(): void {
        SimpleLightSystemManager.instance.systemScript = this;
        this.SetDirty();
    }

    protected start() {
        this.SetDirty();
    }

    SetDirty() {
        SimpleLightSystemManager.instance.isDirty = true;
    }
}