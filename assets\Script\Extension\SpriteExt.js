

/** copy from RESOURCES */
SpriteType = cc.Enum({
    /**
     * !#en The simple type.
     * !#zh 普通类型
     * @property {Number} SIMPLE
     */
    SIMPLE: 0,
    /**
     * !#en The sliced type.
     * !#zh 切片（九宫格）类型
     * @property {Number} SLICED
     */
    SLICED: 1,
    /**
     * !#en The tiled type.
     * !#zh 平铺类型
     * @property {Number} TILED
     */
    TILED: 2,
    /**
     * !#en The filled type.
     * !#zh 填充类型
     * @property {Number} FILLED
     */
    FILLED: 3,
    /**
     * !#en The mesh type.
     * !#zh 以 Mesh 三角形组成的类型
     * @property {Number} MESH
     */
    MESH: 4,
    /**
     * !#en Custom type.
     * !#zh 自定义类型，使用自定义顶点参数
     * @property {Number} CUSTOM
     */
    CUSTOM: 5,
});

// cc.game.once(cc.game.EVENT_ENGINE_INITED, function () {
//     cc.js.mixin(cc.Sprite.prototype, {
//     })
// })

// cc.game.once(cc.game.EVENT_ENGINE_INITED, function () {
    cc.js.mixin(cc.Sprite, {
        Type: SpriteType,
        statics: {
            Type: SpriteType,
        },
    })
// })


// cc.js.mixin(cc.Sprite.prototype.properties, {
        
//     // _type: SpriteType.SIMPLE,
//     // /**
//     //  * !#en The sprite render type.
//     //  * !#zh 精灵渲染类型
//     //  * @property type
//     //  * @type {Sprite.Type}
//     //  * @example
//     //  * sprite.type = cc.Sprite.Type.SIMPLE;
//     //  */
//     // type: {
//     //     get () {
//     //         return this._type;
//     //     },
//     //     set (value) {
//     //         if (this._type !== value) {
//     //             this._type = value;
//     //             this.setVertsDirty();
//     //             this._resetAssembler();
//     //         }
//     //     },
//     //     type: SpriteType,
//     //     animatable: false,
//     //     tooltip: CC_DEV && 'i18n:COMPONENT.sprite.type',
//     // },

//     isRendererDirty: false,
            
//     ratio: {
//         get () { return this._ratio; },
//         set (value) {
//             let srcValue = this._ratio;
//             this._ratio = value;
//             if(srcValue != value) {
//                 this.isRendererDirty = true;
//                 // cc.log(`set ratio: ${value}`);
//             }
//         },
//         type: cc.Float,
//     },
//     _ratio: 0.3,

//     hsv: {
//         get () { return this._hsv; },
//         set (value) {
//             let srcValue = this._hsv;
//             this._hsv = value;
//             if(srcValue.h != value.h || srcValue.s != value.s || srcValue.v != value.v) {
//                 this.isRendererDirty = true;
//                 // cc.log(`set ratio: ${value}`);
//             }
//         },
//         type: cc.Object,
//     },
//     _hsv: {h: 0, s: 0, v: 0},
// })

cc.game.once(cc.game.EVENT_ENGINE_INITED, function () {
    const Assembler = cc.Assembler;
    const { MyAssembler } = require("./MyAssembler");

    cc.js.mixin(cc.Sprite.prototype, {

        // get _assembler() {
        //     return this.__assembler;
        // },
        // set _assembler(value) {
        //     this.__assembler = value;
        // },
        // __assembler: null,

        isRendererDirty: false,

        get mixColor() { return this._mixColor; },
        set mixColor(value) {
            let srcValue = this._mixColor;
            this._mixColor = value;
            if(srcValue.r != value.r || srcValue.g != value.g || srcValue.b != value.b || srcValue.a != value.a) {
                this.isRendererDirty = true;
            }
        },
        _mixColor: cc.color(255, 255, 255, 255),
            
        get ratio() { return this._ratio; },
        set ratio(value) {
            let srcValue = this._ratio;
            this._ratio = value;
            if(srcValue != value) {
                this.isRendererDirty = true;
                // cc.log(`set ratio: ${value}`);
            }
        },
        _ratio: 0,

        get hsv() { return this._hsv; },
        set hsv(value) {
            let srcValue = this._hsv;
            this._hsv = value;
            if(srcValue.h != value.h || srcValue.s != value.s || srcValue.v != value.v) {
                this.isRendererDirty = true;
            }
        },
        _hsv: {h: 0, s: 0, v: 0},

        get ambientColor() { return this._ambientColor; },
        set ambientColor(value) {
            let srcValue = this._ambientColor;
            this._ambientColor = value;
            if(srcValue.r != value.r || srcValue.g != value.g || srcValue.b != value.b || srcValue.a != value.a) {
                this.isRendererDirty = true;
            }
        },
        _ambientColor: cc.color(255, 255, 255, 255),

        get isLightReceiver() { return this._isLightReceiver; },
        set isLightReceiver(value) {
            let srcValue = this._isLightReceiver;
            this._isLightReceiver = value;
            if(srcValue != value) {
                this.isRendererDirty = true;
            }
        },
        _isLightReceiver: false,

        get isChangeOpacity() { return this._isChangeOpacity; },
        set isChangeOpacity(value) {
            let srcValue = this._isChangeOpacity;
            this._isChangeOpacity = value;
            if(srcValue != value) {
                this.isRendererDirty = true;
            }
        },
        _isChangeOpacity: false,

        get isToFrag() { return this._isToFrag; },
        set isToFrag(value) {
            let srcValue = this._isToFrag;
            this._isToFrag = value;
            if(srcValue != value) {
                this.isRendererDirty = true;
            }
        },
        _isToFrag: false,

        // 将自定义数据传递给assembler，在设置完所有参数后调用
        FlushProperties() {
            // const myAssembler = ClassFactory.Instance.MyAssembler();
            let assembler = this._assembler;
            if (!assembler)
                return;
            
            assembler.mixColor = this.mixColor;
            assembler.ratio = this.ratio;
            assembler.hsv_h = this.hsv.h;
            assembler.hsv_s = this.hsv.s;
            assembler.hsv_v = this.hsv.v;
            // assembler.ambientColor = this.ambientColor;
            assembler.isLightReceiver = this.isLightReceiver;
            assembler.isChangeOpacity = this.isChangeOpacity;
            assembler.isToFrag = this.isToFrag;

            this.setVertsDirty();
        },

        _resetAssembler () {
            // if(this.type == SpriteType.SIMPLE) {
            if(this.type == SpriteType.CUSTOM) {
                const myAssembler = MyAssembler();
                this.setVertsDirty();
                let assembler = this._assembler = new myAssembler();
                this.FlushProperties();
    
                assembler.init(this);
            } else {
                Assembler.init(this);
                this._updateColor();
                this.setVertsDirty();
            }
        },

        UpdateRenderer() {
            if(this.isRendererDirty) {
                this.FlushProperties();
                this.isRendererDirty = false;
                return true;
            }
            return false;
        },
    })

})
