// Learn TypeScript:
//  - https://docs.cocos.com/creator/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/manual/en/scripting/life-cycle-callbacks.html

import Util from "../Base/Util";
import GameConfigs from "../GameConfigs";
import GameManager from "../GameManager";
import GameUtils, { ObjectPool, UnitInfo } from "./GameUtils";
import LocalUtils from "../LocalUtils";
import MyPhysicsExtension from "../MyPhysics/MyPhysicsExtension";
import Enemy from "../GameStuffAndComps/Enemy";
import GameDirector from "./GameDirector";
import GameStuffManager from "./GameStuffManager";
import GameWorld from "./GameWorld";

const {ccclass, property} = cc._decorator;

@ccclass
export default class EnemyCreator extends cc.Component {

    // 生成位置
    // 生成类型
    // 生成波次
    // 生成频率

    // 怪物更容易生成在移动方向上


    @property(GameWorld)
    targetGameWorld: GameWorld = null;

    @property([cc.Prefab])
    enemyPrefabList: cc.Prefab[] = [];

    get isAllowCreateBoss(): boolean {
        return this._isAllowCreateBoss;
    }

    get enemyNumLimit() {
        return GameConfigs.instance.enemyNumLimit;
    }

    // leftSide = -3500;
    // rightSide = 2800;
    // upSide = 3100;
    // downSide = -2400;
    leftSide = -2800 + 100;
    rightSide = 3400 - 100;
    upSide = 2400 - 100;
    downSide = -3800 + 100;

    BossHP = 400;

    createWaveTime = 0.4;
    bigWaveCreateTimeInterval = 10;
    frontWaveCreateTimeInterval = 10;
    
    createBossTime = 10;
    createCenterPosList = [cc.v2(1000, -1000), cc.v2(1400, -350), cc.v2(2200, -850)];
    // createEnemyRadius = 3000;

    /** @dirRange 每两个数字代表两个角度之间的范围，角度已向上方向为 0 度，逆时针方向为正数 */
    createPoints: {centerPos: cc.Vec2, radius: number, dirRange: number[]}[] = [
        {centerPos: cc.v2(1000, -1000), radius: 3000, dirRange: [125, 145]},
        {centerPos: cc.v2(1400, -350), radius: 1500, dirRange: [-20, 80]},
        {centerPos: cc.v2(2200, -850), radius: 1500, dirRange: [-130, -40]},
    ];

    enemyPools: ObjectPool<UnitInfo>[] = [];
    enemyNodePools: cc.NodePool[] = [];

    createMode: EnemyCreateMode;

    private _isCreating = false;
    private _creatingTime = 0;
    private _isCreated = false;

    private _isAllowCreateBoss = false;
    private _readyToCreateBossTime = 0;
    private _isReadyToCreateBoss = false;

    private _isCreatingBoss = false;
    private _creatingBossTime = 0;
    private _isBossCreated = false;

    private _isAllowCreateBigWave = true;
    // private _isAllowCreateFrontWave = true;
    private _bigWaveCreateTime = 0;
    private _frontWaveCreateTime = 0;
    // private _isBigWaveCreated = false;

    private _isLastWaveCreate = false;
    private _isLastWaveCreating = false;
    private _isLastWaveCreated = false;

    private _dirRanges: cc.Vec2[] = [];
    private _dirRangesIsFull: boolean[] = [];
    private _refreshDirRangesTime = 0;
    private _refreshDirRangesTimeInterval = 1;

    // LIFE-CYCLE CALLBACKS:

    onLoad () {
        GameUtils.rootEnemyCreator = this;

        this.createMode = EnemyCreateMode.center;
        // this.createMode = EnemyCreateMode.followHero;
        this.InitDirRanges();
        this.InitEnemyPools();
    }
    start () {
        GameManager.instance.AddGameUpdate('EnemyCreator', (dt: number)=>{
            this.gameUpdate(dt);
        });
        // GameUtils.rootLayer_1.HideBossComing();
        // GameUtils.rootLayer_1.HideBossProgress();
    }
    // update (dt) {}

    StartCreate() {
        this._isCreating = true;
        this._isAllowCreateBigWave = true;
    }

    InitDirRanges() {
        for(let i = 0; i < 60; i++) {
            this._dirRanges[i] = LocalUtils.AngleToVec2(360 / 60 * i + 1/2);
            this._dirRangesIsFull[i] = false;
        }
    }

    RefreshDirRanges() {
        let enemys = GameUtils.enemyList;
        let dirRangeEnemyNums: number[] = [];
        let logText = '';
        for(let i = 0; i < 60; i++) {
            dirRangeEnemyNums[i] = 0;
        }
        enemys.forEach((e)=>{
            let centerPos = this.createCenterPosList[1];
            if(GameDirector.instance.gamePhase < 3) {
                centerPos = this.createCenterPosList[0];
            }
            let dir = e.script.rootPosition.sub(centerPos).normalize();
            let angle = LocalUtils.Vec2ToAngle(dir);
            if(angle < 0) {
                angle += 360;
            }
            let dirRangeIndex = Math.floor(angle / (360 / 60) + 1/2);
            dirRangeEnemyNums[dirRangeIndex] += 1;
        });
        for(let i = 0; i < 60; i++) {
            if(dirRangeEnemyNums[i] > 3) {
                this._dirRangesIsFull[i] = true;
                logText = logText + '❌';
            } else {
                this._dirRangesIsFull[i] = false;
                logText = logText + '⚪';
            }
        }
        // console.log(`方向范围内怪物满额情况：\n${logText}`);
    }

    InitEnemyPools() {
        for(let i = 0; i < 8; i++) {
            this.enemyPools[i] = new ObjectPool<UnitInfo>();
            this.enemyNodePools[i] = new cc.NodePool();
            this.enemyPools[i].SetOnPutCallback((info: UnitInfo)=>{
                info.node.active = false;
                this.enemyNodePools[i].put(info.node);
            });
            this.enemyPools[i].SetOnTakeCallback((info: UnitInfo)=>{
                info.node.active = true;
                let node = this.enemyNodePools[i].get();
                node.parent = this.targetGameWorld.MiddleNode;
                info.node = node;
            });
        }
    }

    gameUpdate(dt: number) {
        this._refreshDirRangesTime += dt;
        if(this._refreshDirRangesTime > this._refreshDirRangesTimeInterval) {
            this.RefreshDirRanges();
            this._refreshDirRangesTime = 0;
        }
        // if(this._isAllowCreateBoss) {
        //     this._readyToCreateBossTime += dt;
        //     if(!this._isReadyToCreateBoss && this._readyToCreateBossTime > 10) {
        //         this._isReadyToCreateBoss = true;
        //         GameUtils.rootLayer_1.ShowBossComingSoon();
        //         GameUtils.rootLayer_1.ShowBossProgress();
        //         GameUtils.rootLayer_1.RefreshBossProgressBar(1);
        //         if(this._isLastWaveCreate && !this._isLastWaveCreating) {
        //             this._isLastWaveCreating = true;
        //             GameUtils.rootLayer_1.ShowLastWave();
        //         }
        //         this._isCreatingBoss = true;
        //         this._creatingBossTime = 0;
        //         this._isBossCreated = false;
        //         this.NotAllowCreateBoss();
        //     }
        // }

        if(this._isCreating && GameConfigs.instance.isEnemyGenerate) {
            this._creatingTime += dt;
            this.TryResetCreateWaveTime();
            // if(this._isCreatingBoss) {
            //     this._creatingBossTime += dt;
            //     GameUtils.rootLayer_1.RefreshBossProgressBar(1 - this._creatingBossTime / this.createBossTime);

            //     if(!this._isBossCreated && this._creatingBossTime > this.createBossTime) {
            //         this._isBossCreated = true;
            //         GameUtils.rootLayer_1.HideBossProgress();
            //         GameUtils.rootLayer_1.ShowBossComing();
            //         this._isCreatingBoss = false;
            //     }
            // }
            if(this._isAllowCreateBigWave) {
                this._bigWaveCreateTime += dt;
            }
            // if(this._isAllowCreateFrontWave) {
            //     this._frontWaveCreateTime += dt;
            // }
        }
        if(!this._isCreated && this._creatingTime > this.createWaveTime) {
            // this._isCreated = true;
            if(this._isBossCreated) {
                this.CreateABoss();
                if(this._isLastWaveCreating) {
                    this._isLastWaveCreated = true;
                    GameDirector.instance.isReadyToWin = true;
                }
                this._isBossCreated = false;
                this._creatingBossTime -= this.createBossTime;
            } else {
                if(this._bigWaveCreateTime > this.bigWaveCreateTimeInterval) {
                    this._bigWaveCreateTime = 0;
                    this.CreateBigWave();
                }
                // if(this._frontWaveCreateTime > this.frontWaveCreateTimeInterval) {
                //     this._frontWaveCreateTime = 0;
                //     this.CreateFrontWave();
                // }
                this.CreateWave();
            }
            this._creatingTime = this._creatingTime - this.createWaveTime;
        }
    }

    // 怪物强度与玩家战斗力相关
    TryResetCreateWaveTime() {
        // let param1 = GameConfigs.instance.EnemyCreateWaveTimeParam1;
        // let param2 = GameConfigs.instance.EnemyCreateWaveTimeParam2;
        // let param3 = GameConfigs.instance.EnemyCreateWaveTimeParam3;
        // let param4 = GameConfigs.instance.EnemyCreateWaveTimeParam4;
        let param1 = 0.6;
        let param2 = 0.3;
        let param3 = 12;
        let param4 = 0.3;


        let AttackSpeedUp = GameUtils.mainHero.script.weaponList[0].weaponBuffs.attackSpeedUp;
        let bulletCopy = GameUtils.mainHero.script.weaponList[0].weaponBuffs.bulletCopy;
        let heroNum = GameUtils.heroNum;

        // this.createWaveTime = 2.4 /(1 + AttackSpeedUp * 0.008 + bulletCopy * 0.5 + (heroNum + 2) * 0.4);
        // this.BossHP = 400 * (1 + AttackSpeedUp * 0.008 + bulletCopy * 0.5 + (heroNum + 2) * 0.4);
        
        this.BossHP = 400;

        // this.createWaveTime = 0.7 / (1 + (heroNum - 1) * 0.5);
        // this.bigWaveCreateTimeInterval = 12 / (1 + (heroNum - 1) * 0.7);
        // this.createWaveTime = param1 / (1 + (heroNum - 1) * param2);
        // this.bigWaveCreateTimeInterval = param3 / (1 + (heroNum - 1) * param4);

        let enemyNum = GameUtils.enemyList.length;
        let npcNum = GameStuffManager.instance.npcList.length;
        if(enemyNum < 20) {
            this.createWaveTime = 0.01;
        } else if(enemyNum < 40) {
            this.createWaveTime = 0.01 + (enemyNum - 20) * 0.001; // 0.001 * 20 = 0.02
        } else if(enemyNum < 100) {
            this.createWaveTime = 0.03 + (enemyNum - 40) * 0.002; // 0.002 * 60 = 0.12
        } else if(enemyNum < 200) {
            this.createWaveTime = 0.15 + (enemyNum - 100) * 0.003; // 0.003 * 100 = 0.3
        } else {
            this.createWaveTime = 1;
        }

        this.createWaveTime *= 10 / (1 + (heroNum - 1) * 0.5);
        let isTowerUnlocked = GameDirector.instance.GetGameState('first_tower_unlock');
        if(isTowerUnlocked) {
            this.createWaveTime *= 0.7;
        }

        this.bigWaveCreateTimeInterval = this.createWaveTime * 40;
        this.frontWaveCreateTimeInterval = 15 * (1 - (npcNum * 0.01 > 0.8 ? 0.8 : npcNum * 0.01));
    }

    ReadyToCreateLastWave() {
        this._isLastWaveCreate = true;
    }

    AllowCreateBoss() {
        this._isAllowCreateBoss = true;
        this._isReadyToCreateBoss = false;
        this._readyToCreateBossTime = 0;
    }

    NotAllowCreateBoss() {
        this._isAllowCreateBoss = false;
    }

    // ReadyToCreateBoss() {
    //     this._isCreatingBoss = true;
    //     this._creatingBossTime = 0;
    //     GameUtils.rootLayer_1.ShowBossComingSoon();
    // }

    CreateWave() {
        // this._isCreated = false;
        if(GameUtils.enemyList.length >= this.enemyNumLimit) {
            return;
        }
        if(this.createMode == EnemyCreateMode.followHero) {
            let mainHero = GameUtils.mainHero.script;
            if(mainHero.isMoving) {
                this.CreateAEnemyFromDir(GameUtils.mainHero.script.attribute.moveDir, 80);
            } else {
                this.CreateAEnemy();
            }
        } else if(this.createMode == EnemyCreateMode.center) {
            this.CreateAEnemy();
        }
    }

    CreateBigWave(centerPos: cc.Vec2 = null) {
        if(!centerPos) {
            centerPos = this.GetRandomPos(1);
        }
        // console.log(`CreateBigWave() 生成大波！坐标: ${centerPos.toString()}`);
        let enemyInfo = this.CreateAEnemy(centerPos, 5);
        // (enemyInfo.script as Enemy).isAllowJumpingSplitting = true;
        GameManager.instance.LateFrameCall(()=>{
            if(!enemyInfo || !enemyInfo.script.isAlive) { return; }
            this.AddBossPointDirection(enemyInfo);
        });
        for(let i = 0; i < 15; i++) {
            if(GameUtils.enemyList.length >= this.enemyNumLimit) {
                break;
            }
            let randomDis = 150 + Math.random() * 100;
            let randomDir = LocalUtils.AngleToVec2(Math.random() * 360);
            let pos = centerPos.add(randomDir.mul(randomDis));
            this.CreateAEnemy(pos);
        }
    }

    // CreateFrontWave() {
    //     // console.log('前方生成！');
    //     let centerPos = this.GetRandomPos();
    //     let enemyInfo = this.CreateAEnemy(centerPos, 5);
    //     GameManager.instance.LateFrameCall(()=>{
    //         if(!enemyInfo || !enemyInfo.script.isAlive) { return; }
    //         this.AddBossPointDirection(enemyInfo);
    //     });
    //     for(let i = 0; i < 15; i++) {
    //         if(GameUtils.enemyList.length >= this.enemyNumLimit) {
    //             break;
    //         }
    //         let randomDis = 150 + Math.random() * 100;
    //         let randomDir = LocalUtils.AngleToVec2(Math.random() * 360);
    //         let pos = centerPos.add(randomDir.mul(randomDis));
    //         this.CreateAEnemy(pos);
    //     }
    // }

    CreateFirstWave() {
        this.CreateBigWave(cc.v2(-700, -2200));
        this.CreateBigWave(cc.v2(-200, -2500));
    }

    CreateLastWave(delayTime = 1, isFromAbsorb: boolean = false) {
        if(!this._isLastWaveCreated) {
            this._isLastWaveCreated = true;
            // GameDirector.instance.isAllowAbsorb = false;
            GameDirector.instance.isReadyToWin = true;
            GameManager.instance.LateTimeCallOnce(()=>{
                GameUtils.rootLayer_1.ShowBossComing();
                // this.CreateABoss(GameUtils.rootGuideToStatue.statues[0].rootPosition.add(cc.v2(0, -150)), isFromAbsorb);
                this.CreateABoss();
            }, delayTime);
            return true;
        }
        return false;
    }

    CreateAEnemy(pos?: cc.Vec2, prefabIndex: number = -1) {
        if(!pos) {
            pos = this.GetRandomPos();
        }
        if(this.CheckPosIsFine(pos)) {
            if(prefabIndex >= 0) {
                return this.GenerateEnemy(pos, prefabIndex);
            } else {
                return this.GenerateRandomEnemy(pos);
            }
        } else {
            // console.warn(`CreateAEnemy() 失败：未生成敌人！坐标位置无效: ${pos.toString()}`);
        }
        // this.GenerateEnemy(pos, 0);
        return null;
    }

    CreateABoss(pos?: cc.Vec2, isFromAbsorb: boolean = false) {
        if (!pos) {
            pos = this.GetRandomPos(0, 150);
        }
        return this.GenerateEnemy(pos, 6, true, isFromAbsorb);
    }

    CreateAEnemyFromDir(srcDir: cc.Vec2, range: number, OutMargin = 50) {
        let randomRotate = Util.RandomRange(-range, range);
        let dir = srcDir.rotate(randomRotate * Math.PI / 180);
        let pos = this.GetRectPos(dir, OutMargin);
        let loopTimes = 0;
        while (loopTimes < 100 && !this.CheckPosIsFine(pos)) {
            loopTimes += 1;
            pos = this.GetRandomPos(0, OutMargin);
        }
        if(this.CheckPosIsFine(pos)) {
            return this.GenerateRandomEnemy(pos);
        }
        return null;
    }

    GenerateRandomEnemy(pos: cc.Vec2) {
        let randomEnemyRangeIndex = Util.RandomRange(0, 100);
        let enemyPrefabIndex = 0;
        // if(randomEnemyRangeIndex < 40) {
        //     enemyPrefabIndex = 0;
        // } else if(randomEnemyRangeIndex < 70) {
            enemyPrefabIndex = 5;
        // } else if(randomEnemyRangeIndex < 95) {
        // } else {
        //     enemyPrefabIndex = 2;
        // // } else {
            // enemyPrefabIndex = 7;
        // }
        return this.GenerateEnemy(pos, enemyPrefabIndex);
    }

    GenerateEnemy(pos: cc.Vec2, enemyPrefabIndex = -1, isBoss = false, isFromAbsorb = false): UnitInfo {
        enemyPrefabIndex = enemyPrefabIndex >= 0 ? enemyPrefabIndex : 2;
        let prefab = this.enemyPrefabList[enemyPrefabIndex];

        let enemyInfo: UnitInfo = null;
        let enemyScript: Enemy = null;
        // if(!isBoss) { 
            enemyInfo = this.TryGenerateFromNodePool(enemyPrefabIndex);
        // }
        if(enemyInfo == null) {
            let node = LocalUtils.GenerateNode(this.targetGameWorld.MiddleNode, prefab, cc.v3());
            enemyScript = node.getComponent(Enemy);
            enemyInfo = new UnitInfo(node);
            enemyInfo.script = enemyScript;
            enemyScript.info = enemyInfo;
            enemyScript.OnBorn();
            GameManager.instance.LateFrameCall(()=>{
                enemyScript.rootPosition = pos;
            });
        } else {
            enemyInfo.node.opacity = 0;
            enemyScript = enemyInfo.script as Enemy;
            enemyScript.Reset(pos);
        }

        GameStuffManager.instance.PushEnemyInfo(enemyInfo);
        if(enemyScript.enemyRef == 7) {
            GameStuffManager.instance.mainBoss = enemyInfo;
            this.ConfigureBossAttributes(enemyScript, isFromAbsorb);
            // GameUtils.rootGameWorldUI.GenerateNewEnemyHPBar(enemyScript as Enemy);
        } else if(enemyScript.enemyRef == 4 || enemyScript.enemyRef == 6) {
            GameUtils.rootGameWorldUI.GenerateNewEnemyHPBar(enemyScript as Enemy);
        }
        return enemyInfo;
    }

    ConfigureBossAttributes(enemy: Enemy, isFromAbsorb: boolean) {
        if (isFromAbsorb) {
            // 吸收进度满生成的 Boss 属性
            enemy.attribute.maxHp = 100000;
            enemy.attribute.nowHp = 100000;
            enemy.attribute.moveSpeed = 530;
            enemy.weaponList[enemy.mainWeaponIndex].weaponBuffs.damageUp = 300;
        } else {
            // 雕像解锁生成的 Boss 属性
            enemy.attribute.maxHp = 8000;
            enemy.attribute.nowHp = 8000;
            enemy.attribute.moveSpeed = 500;
            enemy.weaponList[enemy.mainWeaponIndex].weaponBuffs.damageUp = 100;
        }
    }

    TryGenerateFromNodePool(enemyPrefabIndex: number): UnitInfo {
        let enemyInfo: UnitInfo = null;
        if (this.enemyPools[enemyPrefabIndex].Size() > 0) {
            enemyInfo = this.enemyPools[enemyPrefabIndex].Take();
            // console.log(`成功从对象池中取出对象！ id: ${enemyInfo.script.unit_id}`);
            return enemyInfo;
        } else {
            // console.log('对象池没有对象！');
            return null;
        }
    }

    TryRecoveryEnemy(info: UnitInfo) {
        let index = (info.script as Enemy).enemyRef - 1;
        // if(index != 3 && index != 6) {
            this.enemyPools[index].Put(info);
            // console.log(`成功向对象池中加入对象！ id: ${info.script.unit_id}`);
            return true;
        // }
        // return false;
    }

    GetRandomPos(ref: number = 0, OutMargin = 50) {
        let isTowerUnlocked = GameDirector.instance.GetGameState('first_tower_unlock');
        // 挑选一个生成点
        let createPoint = this.createPoints[0];
        if(ref == 0 && isTowerUnlocked) {
            createPoint = this.createPoints[Math.random() > 0.7 ? (Math.random() > 0.5 ? 1 : 2) : 0];
        }
        let centerPos = createPoint.centerPos;

        let randomDir = EnemyCreator.GetRandomDir(createPoint.dirRange);

        // 在指定方向上的怪物数量不能太多
        let loopTimes = 0;
        // while(loopTimes < 100 && this.IsDirRangeIsFull(randomDir)) {
        //     loopTimes += 1;
        //     randomDir = LocalUtils.AngleToVec2(Util.RandomRange(-50, -35), cc.v2(0, 1));
        // }
        // this.IsDirRangeIsFull(randomDir);

        let pos = cc.v2();
        if(this.createMode == EnemyCreateMode.followHero) {
            pos = this.GetRectPos(randomDir, OutMargin);
        } else if(this.createMode == EnemyCreateMode.center) {
            pos = this.GetCenterOutsidePos(centerPos, randomDir, createPoint.radius);
        }
        loopTimes = 0;
        while(loopTimes < 5 && !this.CheckPosIsFine(pos)) {
            loopTimes += 1;
            let randomDir = cc.v2(1, 0);
            // if(Math.random() < frontRitio || dirRef == 1) {
                randomDir = EnemyCreator.GetRandomDir(createPoint.dirRange);
            // } else {
            //     randomDir = LocalUtils.AngleToVec2(Util.RandomRange(-180, 180), cc.v2(0, 1));
            // }
            if(this.createMode == EnemyCreateMode.followHero) {
                pos = this.GetRectPos(randomDir, OutMargin);
            } else if(this.createMode == EnemyCreateMode.center) {
                pos = this.GetCenterOutsidePos(centerPos, randomDir, createPoint.radius);
            }
        }
        return pos;
    }

    /** @dirRange 每两个数字代表两个角度之间的范围，角度以向上7方向为 0 度，逆时针方向为正数 */
    static GetRandomDir(dirRange: number[]): cc.Vec2 {
        // 在指定范围内随机方向
        let randomDir = cc.v2(1, 0);
        let ranges: {start: number, range: number, offset: number}[] = [];
        let curOffset = 0;
        dirRange.forEach((e, i, arr)=>{
            if(i % 2 == 1) {
                let start = arr[i - 1];
                let range = start - e;
                ranges.push({start: start, range: range, offset: curOffset});
                curOffset += range;
            }
        });
        let randomOffset = Util.RandomRange(0, curOffset);
        let randomOffsetReduce = 0;
        ranges.forEach((e, i)=>{
            if(randomOffset > e.offset) { return; }
            randomOffsetReduce += e.range;
            randomDir = LocalUtils.AngleToVec2(dirRange[i*2] + randomOffset - randomOffsetReduce, cc.v2(0, 1));
        });
        return randomDir;
    }

    IsDirRangeIsFull(dir: cc.Vec2) {
        let angle = LocalUtils.Vec2ToAngle(dir);
        if(angle < 0) {
            angle += 360;
        }
        let dirRangeIndex = Math.floor(angle / (360 / 60) + 1/2);
        return this._dirRangesIsFull[dirRangeIndex];
    }
    
    // 获取中心点外围的点
    GetCenterOutsidePos(centerPos: cc.Vec2, dir: cc.Vec2, outsideDistance: number) {
        let fake3dDistance = outsideDistance * GameUtils.Fake3dDistance(cc.v2(), dir);
        return centerPos.add(dir.mul(fake3dDistance));
    }

    // 计算边缘
    GetRectPos(dir: cc.Vec2, OutMargin: number) {
        let mainHeroPos = GameUtils.mainHero.script.rootPosition;
        return mainHeroPos.add(this.GetRectPosFromDir(dir, GameUtils.gameWorldRectSize.width, GameUtils.gameWorldRectSize.height, OutMargin));
    }

    GetRectPosFromDir(dir: cc.Vec2, width: number, height: number, OutMargin: number) {
        let widthScale = Math.abs((width/2 + OutMargin) / dir.normalize().x);
        let heightScale = Math.abs((height/2 + OutMargin) / dir.normalize().y);
        let scale = widthScale < heightScale ? widthScale : heightScale;
        let pos = dir.normalize().mul(scale);
        return pos;
    }

    CheckPosIsFine(pos: cc.Vec2) {
        // 可检测是否在碰撞体范围内
        let worldPos = GameUtils.rootGameWorld.MiddleNode.convertToWorldSpaceAR(pos);
        let rect = cc.rect(worldPos.x - 10, worldPos.y - 15, 20, 30);
        let result = MyPhysicsExtension.Instance.MyTestAABB(rect);
        if(result.length > 0) {
            let isCan = true;
            result.forEach((e)=>{
                if(isCan && e.node.name != 'GameWorldDriftingWall') {
                    // console.log(`区域内检测到碰撞体！${e.node.name}, pos: ${pos}, worldPos: ${worldPos}`);
                    isCan = false;
                }
            });
            if(!isCan) {
                return false;
            }
        }
        if(this.createMode == EnemyCreateMode.followHero) {
            return this.CheckPosIsInSide(pos);
        }
        // let posSub = pos.sub(GameUtils.mainHero.script.rootPosition);
        // let dir = posSub.normalize();
        // let distance = posSub.len();
        // let rectPos = this.GetRectPosFromDir(dir, GameUtils.gameWorldRectSize.width * 0.8, GameUtils.gameWorldRectSize.height * 0.8, 50);
        // let rectPosDistance = rectPos.len();
        // return distance > rectPosDistance;
        return true;
    }

    CheckPosIsInSide(pos: cc.Vec2) {
        if(pos.x > this.leftSide && pos.x < this.rightSide && pos.y > this.downSide && pos.y < this.upSide) {
            return true;
        }
        return false;
    }
    
    AddBossPointDirection(enemyInfo: UnitInfo) {
        let pointID = GameUtils.rootPointDirectionPanel.AddPointDirection(enemyInfo.script.centerPosition, 150, 300);
        let time = 0;
        GameManager.instance.AddGameUpdate('EnemyCreator#CreateBigWave', (dt: number)=>{
            time += dt;
            if(enemyInfo.script.isAlive && time < 60) {
                GameUtils.rootPointDirectionPanel.UpdatePointDirection(pointID, enemyInfo.script.centerPosition);
            } else {
                GameUtils.rootPointDirectionPanel.RemovePointDirectionByID(pointID);
                GameManager.instance.RemoveUpdateCallback();
            }
        });
    }

}

export enum EnemyCreateMode {
    followHero,
    center,
}