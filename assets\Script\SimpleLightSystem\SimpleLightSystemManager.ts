
/** 简易光照系统
 * 1. 主要支持点光源，设置环境光
 * 2. 光源影响范围内的所有接收器，都会计算光照
 * 3. 材质不变
 * 4. 点光源参数：位置、y 轴缩放、颜色、强度、内部半径、外部半径、衰减指数
 * 5. 点光源先被接收器计算，计算得出光照在本节点的光照中心上的强度
 * 6. 材质参数只传入最终的光照颜色和强度，影响是全局的（或在着色器内计算）
 */

import CustomSpriteData from "../CustomSpriteData";
import GameUtils from "../Game/GameUtils";
import DataTexture from "./DataTexture";
import SimpleLightSource from "./SimpleLightSource";
import SimpleLightSystem from "./SimpleLightSystem";

export default class SimpleLightSystemManager {

    public static get instance(): SimpleLightSystemManager {
        if(!this._instance) {
            this._instance = new SimpleLightSystemManager();
        }
        return this._instance;
    }
    private static _instance: SimpleLightSystemManager = null;

    get ambientFinalColor(): cc.Color {
        let color = this.ambientColor.clone();
        if(this.ambientIntensity <= 1) {
            color.a = this.ambientIntensity * 255;
        } else {
            color.a = 255;
        }
        return color;
    }

    get ambientColor(): cc.Color {
        return this.systemScript.ambientColor;
    }

    get ambientIntensity(): number {
        return this.systemScript.ambientIntensity;
    }
    
    systemScript: SimpleLightSystem = null;

    lightList: SimpleLightInfo[] = [];

    // lightReceiverList: CustomSpriteData[] = [];

    get isDirty() { return this._isDirty; }
    private _isDirty = false;
    isResetDirty = false;

    SetDirty() {
        this._isDirty = true;
        this.isResetDirty = false;
    }

    update(dt: number) {
        if(this.isResetDirty) {
            if(this._isDirty) {
                this._isDirty = false;
            }
            this.isResetDirty = false;
        }
        if(this._isDirty) {
            this.WriteInLights();
            if(!this.isResetDirty) {
                this.isResetDirty = true;
            }
        }
        // this.RefreshAmbient();
        // this.WriteInLights();
    }

    /** 测试：仅写入环境光 */
    RefreshAmbient() {
        let ambient = this.ambientFinalColor;
        this.systemScript.dataTexture = DataTexture.WriteTestColor(ambient);
        if(this.systemScript.dataTexture) {
            this.systemScript.customSpriteMaterial.setProperty('dataTexture', this.systemScript.dataTexture);
        }
    }

    /** 将光照数据写入纹理 */
    WriteInLights() {
        let ambient = this.ambientFinalColor;
        this.systemScript.dataTexture = DataTexture.WriteParametricLightsMap(ambient, this.lightList);
        if(this.systemScript.dataTexture) {
            this.systemScript.customSpriteMaterial.setProperty('dataTexture', this.systemScript.dataTexture);
        }
    }

    /** 注册一个灯光 */
    regLight(script: SimpleLightSource) {
        let info = this.findLight(script);
        if(!info) {
            info = new SimpleLightInfo(script);
            this.lightList.push(info);
            info.type = script.lightType;
            cc.log(`regLight: ${info.id}`);
        }
        return info;
    }

    updateLight(id: number) {
        let info = this.lightList.find((e)=>{
            return e.id == id;
        });
        if(info) {
            this.SetDirty();
        }
    }

    findLight(script: SimpleLightSource) {
        let foundInfo = this.lightList.find((e)=>{
            return e.script == script;
        });
        return foundInfo;
    }
}

export class SimpleLightInfo {
    private static _curID = 0;

    id: number = -1;
    type: SimpleLightType = SimpleLightType.none;
    script: SimpleLightSource = null;

    constructor(script: SimpleLightSource) {
        this.id = SimpleLightInfo.GetID();
        this.script = script;
    }

    static GetID() {
        this._curID ++;
        return this._curID;
    }
}

export enum SimpleLightType {
    none,
    point,
    polygon,
    sprite,
}
